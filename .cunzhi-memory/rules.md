# 开发规范和规则

- 开发规范：1.遵循项目现有的代码风格和架构模式 2.新功能开发需要包含Controller-Service-DAO三层 3.使用MyBatis Plus进行数据库操作 4.统一使用CommonResult封装响应 5.添加Swagger注解用于API文档 6.使用@PreAuthorize进行权限控制 7.异常处理使用CrmebException 8.参数验证使用@Validated注解 9.实体类使用Lombok注解简化代码
- CRMEB官方开发规范：1.遵循标准RESTful接口设计原则 2.使用Spring Security进行权限管理，权限可精确到按钮级别 3.前后端分离开发模式 4.使用Redis进行缓存和队列处理 5.支持多端部署(H5、公众号、小程序、APP) 6.数据统计使用ECharts图表 7.表单使用Vue拖拽生成控件 8.遵循SpringBoot最佳实践
- 金梭酒店项目定时任务策略已确定：采用混合策略(智能插入+更新)，根据入住时间距离分层保护(7天外可更新价格，3-7天无订单时更新价格，1-3天只更新库存，24小时内完全锁定)，配套数据清理机制(30天自动清理)，批量处理优化性能，包含完整监控告警体系。
- 金梭酒店项目已确定不扩展现有表结构：删除了eb_product表扩展字段的方案，改为通过SKU编码识别酒店商品(HOTEL_{商户ID}_{房间ID}_{入住日期})，完全通过业务逻辑层处理混合策略，不修改任何现有表结构，符合用户明确的"不扩展现有表"要求。
- 金梭酒店项目中国日历接口设计方案已完成：基于https://unpkg.com/holiday-calendar@1.1.9/data/CN/2025.json接口，设计了完整的日历服务包括日期类型判断(工作日/周末/节假日/调休)、价格策略匹配逻辑(节假日>周末>基础价格)、三级缓存策略(本地+Redis+远程)、批量查询接口、缓存预热和监控机制，确保高性能和高可用性。
- 金梭酒店项目中国日历接口架构设计已确定：按CRMEB模块职责分布，crmeb-common存放常量枚举和请求响应类，crmeb-service实现核心业务逻辑和缓存服务，crmeb-admin提供后台管理接口，crmeb-front提供前端查询接口，完全遵循项目架构规范，职责清晰便于维护扩展。
- 修复了酒店取消费用计算逻辑错误：原逻辑使用findFirst()会匹配第一个满足条件的规则，修正为使用max()找到最大的但不超过提前小时数的规则，确保按照文档规则正确匹配(如提前4221小时应匹配48小时规则而非2小时规则)。
- 完全重构了酒店取消费用计算逻辑：1.修复了区间匹配逻辑，严格按照文档规则(提前<2小时不可取消，12-24小时扣10%，24-48小时扣5%，≥48小时免费)；2.添加了不可取消状态(-1)的处理；3.更新了前端显示逻辑，正确处理不可取消、免费取消、收费取消三种状态；4.修复了axios响应数据解析问题。
- 修复酒店取消费用计算的最终问题：1.重新理解数据库规则配置，penalty_value=100%表示不可取消而非收费100%；2.修改匹配逻辑为降序排序，找到第一个满足advanceHours>=rule.getAdvanceHours()的规则；3.特殊处理penalty_value=100的情况，返回-1表示不可取消；4.确保提前2小时显示"不可取消"而非收费。
- 修复酒店管理页面编辑和查看功能数据传递问题：1.在RoomForm.vue、PriceForm.vue、CancelForm.vue组件中添加formData的深度监听器；2.设置immediate:true确保组件初始化时就能获取数据；3.确保编辑和查看按钮点击时能正确显示表单数据；4.解决了三个酒店管理页面的数据传递问题。
- 修复价格日历组件渲染错误：1.在PriceCalendar.vue中添加安全检查，防止访问undefined的dayPrices属性；2.在接口调用时添加错误处理和数据结构验证；3.添加resetCalendarData方法确保数据初始化；4.解决了"Cannot read properties of undefined (reading 'dayPrices')"的Vue渲染错误。
- 修复价格日历数据显示问题：接口返回的数据结构是直接的对象(response)而不是response.data，修正了数据访问路径从response.data改为response，解决了"暂无数据"的显示问题。
- 金梭酒店项目开发规范总结：1.严格遵循用户要求-不生成总结性Markdown文档、不生成测试脚本、不编译、不运行；2.代码质量要求-所有修改必须有中文注释说明意图，保持修改范围最小化，避免不必要的代码更改；3.架构一致性-严格按照CRMEB现有架构模式开发，不扩展现有表结构，最大化复用现有功能；4.业务逻辑-价格计算基于中国日历接口和策略类型匹配，取消规则采用时间分层保护策略，定时任务采用混合策略生成商品；5.前端开发-使用Element UI组件，遵循Vue最佳实践，正确处理axios响应数据结构，添加错误处理和调试日志。
- 酒店管理页面布局已统一修改完成：1.房型管理页面(/hotel/room/)、价格策略页面(/hotel/price/)、取消规则页面(/hotel/cancel/)的操作列布局已统一；2.修改内容-将el-button改为a标签链接样式，添加el-divider竖线分隔符，调整操作列宽度为190px，添加fixed="right"固定右侧；3.保持权限控制逻辑不变，删除操作保持红色样式；4.现在三个页面的操作列布局与商品管理页面完全一致，符合项目整体UI规范。
- 修复酒店取消规则表单字段映射错误：1.前端表单字段与后端Request类不匹配导致提交时必填字段为空的验证错误；2.修改内容-cancelType改为penaltyType、priority改为sort、deductAmount/deductPercentage合并为penaltyValue；3.调整表单验证规则和提交逻辑，确保字段正确映射到后端HotelCancelRuleRequest类；4.修复后前端表单字段与后端完全一致，解决了penaltyType、penaltyValue、sort三个必填字段为空的问题。
- 优化酒店取消规则表单的免费取消逻辑：1.移除了前端"免费取消"独立选项，统一使用后端设计的两种扣费类型(按比例扣费、固定金额扣费)；2.添加明确的说明文字，告知用户通过"按比例扣费，扣费值=0"来实现免费取消；3.更新规则说明部分，明确免费取消的实现方式；4.保持后端逻辑不变，符合文档设计中penalty_value=0.00实现免费取消的方案；5.前端表单现在与后端业务逻辑完全一致，用户体验更清晰。
- 修复酒店取消规则编辑更新报错问题：1.移除了HotelCancelRuleRequest中@DecimalMax(value="100.00")注解限制，因为固定金额扣费应该可以超过100元；2.后端业务层validateCancelTypeParams方法已正确处理分类验证（按比例≤100，固定金额≥0）；3.前端表单验证逻辑也已正确实现分类验证；4.问题根源是注解验证过于严格，现在允许固定金额扣费超过100元。
- 用户明确要求：❌不要生成总结性Markdown文档 ❌不要生成测试脚本 ❌不要编译，用户自己编译 ❌不要运行，用户自己运行
- 严重错误教训：后端Java代码必须放在正确的目录结构中，crmeb-admin、crmeb-service、crmeb-common等是后端模块，dljs-sys-mer-web是前端Vue模块。绝对不能把Java代码写到前端目录下。
- CRMEB项目数据库字段is_del使用tinyint(4)类型，应该使用Integer类型而不是Boolean类型，删除时设置为1，查询时过滤is_del=0
- 金梭酒店定时任务清理策略已优化：从"过期30天后清理"改为"保留7天历史数据"，即清理7天前的过期商品，平衡了性能和数据保留需求
- 用户明确要求：❌不要生成总结性Markdown文档 ❌不要生成测试脚本 ❌不要编译，用户自己编译 ❌不要运行，用户自己运行
- 用户明确要求：❌不要生成总结性Markdown文档 ❌不要生成测试脚本 ❌不要编译，用户自己编译 ❌不要运行，用户自己运行
- CRMEB项目中，后端期望的token header名称是'Authori-zation'而不是标准的'Authorization'，小程序端请求时必须使用'Authori-zation'作为header名称，否则会导致token验证失败提示未登录。
- 已实施Spring Cache + Redis基础缓存方案：商户信息缓存1小时，房型详情缓存30分钟，房型列表查询缓存10分钟，酒店列表缓存15分钟。房型增删改操作会自动清除相关缓存确保数据一致性。
- 修复Spring Cache参数名问题：@Cacheable注解中的key参数名必须与方法参数名完全匹配，如方法参数为id则key应为#id而不是#dictId。已为sysDict添加2小时缓存配置。
- 酒店订单校验规则：在现有CRMEB订单校验基础上，需要为酒店商品添加特殊校验逻辑，包括时间敏感性校验（入住日期不能是过去、退房日期必须晚于入住日期）、酒店商品动态性校验（日期特定商品、价格实时性、库存实时性）、业务逻辑校验（取消规则、连续入住、房间类型匹配）。现有CRMEB校验主要针对静态商品，酒店这种动态时间敏感商品需要额外的专门校验逻辑。
- 严格参照已有的价格查询接口和实体字段，不要私自添加没有的字段。修复代码时要检查类型兼容性问题。
- 修复酒店商品ID为空问题：定时任务HotelProductSyncServiceImpl缺少创建eb_product_attribute记录，导致查询ProductAttrValue时找不到对应的商品属性。解决方案：在createProduct方法中添加createProductAttribute方法，创建ProductAttribute和ProductAttributeOption记录，确保商品属性体系完整。CRMEB系统商品属性包含eb_product_attribute(规格表)、eb_product_attribute_option(规格选项表)、eb_product_attr_value(属性值表)三个表，缺一不可。
- 修复酒店商品ID为空的真正问题：findProductAttrValueId方法缺少type和marketingType字段匹配。CRMEB系统查询ProductAttrValue时必须匹配type字段（酒店商品type=7）和marketingType字段（基础商品marketingType=0），否则查询不到数据。定时任务创建的ProductAttrValue记录是正确的，问题在于查询条件不完整。
- 酒店商品ID为空问题的完整解决方案：1.问题根源是数据库中有酒店商品(eb_product,type=7)但缺少对应的ProductAttrValue记录；2.添加了fixMissingProductAttrValues()方法批量创建缺失的ProductAttrValue记录；3.在HotelBookingController中添加了临时修复接口/fix-missing-attr-values；4.修复后findProductAttrValueId方法就能正确查询到数据，解决前端productId和attrValueId为undefined的问题。
- 定时任务ProductAttrValue创建失败的根本原因：缺少必填字段设置。修复方案：1.在createProductAttrValue方法中添加barCode、expand、cdkeyId等必填字段的默认值；2.使用HotelConstants.HOTEL_PRODUCT_TYPE常量而不是硬编码数字7；3.添加详细的异常处理和日志记录，便于排查问题。定时任务确实在运行（从2025-07-30的商品创建时间可以证明），但ProductAttrValue创建步骤因缺少必填字段而失败。
- 酒店商品ID为空问题的最终解决方案：1.根本原因是定时任务中createProductAttrValue方法缺少必填字段（barCode、expand、cdkeyId）导致ProductAttrValue记录创建失败；2.修复了createProductAttrValue方法，添加了所有必填字段的默认值；3.增强了异常处理和日志记录；4.删除了临时修复接口，让定时任务自然运行来解决问题；5.下次定时任务运行时会正确创建ProductAttrValue记录，findProductAttrValueId方法就能查询到数据，前端productId和attrValueId就不会再是undefined。
- 修复酒店多日期预订Bug：1.问题根源是detail.vue数据转换时丢失了priceDetails中的商品ID信息；2.修复方案：detail.vue保留完整priceInfo数据，booking.vue使用真实价格明细构建多商品预下单；3.后端修改：FrontOrderServiceImpl的buyNow模式支持多商品处理，采用商户聚合逻辑；4.解决了支付4晚但订单只显示1晚的严重Bug，确保每个入住日期都有对应的商品和库存扣减。
