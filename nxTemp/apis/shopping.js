import request from "@/nxTemp/request/ajax.js";

// 获取字典列表
export function getAreaList(pid) {
    return request({
        url: `/api/front/foundation/area/list?pid=${pid}`,
        method: 'GET',
    })
}

// ==================== 订单相关接口 ====================

/**
 * 获取订单列表
 * @param {Object} params - 查询参数
 * @param {Number} params.status - 订单状态（-1：全部，0：待支付，1：待发货，3：待核销，4：待收货，5：已收货，6：已完成，9：已取消）
 * @param {String} params.keywords - 搜索关键字
 * @param {Number} params.page - 页码
 * @param {Number} params.limit - 每页数量
 */
export function getOrderList(params) {
    return request({
        url: '/api/front/order/list',
        method: 'GET',
        params
    })
}

/**
 * 获取订单详情
 * @param {String} orderNo - 订单号
 */
export function getOrderDetail(orderNo) {
    return request({
        url: `/api/front/order/detail/${orderNo}`,
        method: 'GET'
    })
}

/**
 * 取消订单
 * @param {String} orderNo - 订单号
 */
export function cancelOrder(orderNo) {
    return request({
        url: `/api/front/order/cancel/${orderNo}`,
        method: 'POST'
    })
}

/**
 * 删除订单
 * @param {String} orderNo - 订单号
 */
export function deleteOrder(orderNo) {
    return request({
        url: `/api/front/order/delete/${orderNo}`,
        method: 'POST'
    })
}

/**
 * 确认收货
 * @param {String} orderNo - 订单号
 */
export function confirmReceive(orderNo) {
    return request({
        url: `/api/front/order/take/delivery/${orderNo}`,
        method: 'POST'
    })
}