<template>
  <view class="hotel-detail-page">
    <!-- 酒店图片轮播 -->
    <view class="hotel-images">
      <swiper class="swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500">
        <swiper-item v-for="(image, index) in hotelInfo.hotelImages" :key="index">
          <image
              :src="image"
              class="hotel-image"
              mode="aspectFill"
          />
        </swiper-item>
      </swiper>

      <!--      &lt;!&ndash; 返回按钮 &ndash;&gt;-->
      <!--      <view class="back-btn" @click="goBack">-->
      <!--        <text class="back-icon">←</text>-->
      <!--      </view>-->

      <!-- 分享按钮 -->
      <view class="share-btn" @click="shareHotel">
        <text class="share-icon">⋯</text>
      </view>
    </view>

    <!-- 酒店基本信息 -->
    <view class="hotel-info-card">
      <view class="hotel-header">
        <view class="hotel-name">{{ hotelInfo.hotelName }}</view>
        <view class="hotel-rating">
          <view class="stars">
            <text class="star" v-for="n in 5" :key="n" :class="{ active: n <= hotelInfo.starLevel }">★</text>
          </view>
          <text class="score">{{ hotelInfo.score }}分</text>
        </view>
      </view>

      <view class="hotel-location">
        <text class="location-icon">📍</text>
        <text class="location-text">{{ hotelInfo.address }}</text>
        <text class="distance">距离市中心2.5km</text>
      </view>

      <view class="hotel-tags" v-if="hotelInfo.tags && hotelInfo.tags.length">
        <text class="tag" v-for="tag in hotelInfo.tags" :key="tag">{{ tag }}</text>
      </view>
    </view>

    <!-- 酒店设施 -->
    <view class="facilities-section" v-if="hotelInfo.facilities && hotelInfo.facilities.length">
      <view class="section-title">酒店设施</view>
      <view class="facilities-grid">
        <view class="facility-item" v-for="facility in hotelInfo.facilities" :key="facility.name">
          <text class="facility-icon">{{ facility.icon }}</text>
          <text class="facility-name">{{ facility.name }}</text>
        </view>
      </view>
    </view>

    <!-- 酒店介绍 -->
    <view class="intro-section" v-if="hotelInfo.intro">
      <view class="section-title">酒店介绍</view>
      <view class="intro-content">
        <text class="intro-text">{{ hotelInfo.intro }}</text>
        <text class="read-more" @click="toggleIntro">{{ showFullIntro ? '收起' : '展开' }}</text>
      </view>
    </view>

    <!-- 房型选择 -->
    <view class="rooms-section">
      <view class="section-title">选择房型</view>
      <view class="date-selector" @click="goToDateSelect">
        <view class="date-info">
          <text class="date-label">入住</text>
          <text class="date-value">{{ formatDate(checkInDate) }}</text>
        </view>
        <view class="nights-info">
          <text class="nights">{{ nightsCount }}晚</text>
        </view>
        <view class="date-info">
          <text class="date-label">离店</text>
          <text class="date-value">{{ formatDate(checkOutDate) }}</text>
        </view>
        <text class="change-date" @click="goToDateSelect">修改</text>
      </view>

      <view class="room-list">
        <view
          class="room-item"
          v-for="(room, index) in roomList"
          :key="room.id"
          :class="{ 'selected': selectedRoomId === room.id }"
          @click="selectRoom(room, index)"
        >

          <view class="room-image">
            <image :src="room.image" mode="aspectFill"/>
          </view>
          <view class="room-info">
            <view class="room-name">{{ room.name }}</view>
            <view class="room-size">{{ room.size }}㎡</view>
            <view class="room-facilities">
              <text class="room-facility" v-for="facility in room.facilities" :key="facility">{{ facility }}</text>
            </view>
            <view class="room-price">
              <text class="price-symbol">¥</text>
              <text class="price-number">{{ room.price }}</text>
              <text class="price-unit">/晚</text>
            </view>
          </view>
          <view class="room-action">
            <!-- 选择状态指示器 -->
            <view class="select-indicator" v-if="selectedRoomId === room.id">
              <u-icon name="checkmark" size="16" color="#fff"></u-icon>
            </view>
            <button class="book-btn" @click.stop="bookRoom(room)">预订</button>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="price-info">
        <text class="price-label">最低价</text>
        <text class="price-value">¥{{ hotelInfo.minPrice }}/晚</text>
      </view>
      <button class="book-now-btn" @click="quickBook">立即预订</button>
    </view>
  </view>
</template>

<script>
import {getHotelDetail, getRoomList} from '@/nxTemp/apis/hotel.js'

export default {
  name: 'HotelDetail',
  data() {
    return {
      hotelId: null,
      hotelInfo: {
        hotelImages: [],
        facilities: [],
        tags: []
      },
      roomList: [],
      checkInDate: '',
      checkOutDate: '',
      showFullIntro: false,
      // 房型选择状态
      selectedRoomId: '', // 改为空字符串，避免null比较问题
      selectedRoomName: '',
      selectedRoomIndex: -1
    }
  },

  computed: {
    nightsCount() {
      if (!this.checkInDate || !this.checkOutDate) return 1
      const checkIn = new Date(this.checkInDate)
      const checkOut = new Date(this.checkOutDate)
      const diffTime = Math.abs(checkOut - checkIn)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return diffDays || 1
    }
  },

  onLoad(options) {
    if (options.hotelId) {
      this.hotelId = options.hotelId
    }

    // 获取传递的日期参数
    if (options.checkInDate) {
      this.checkInDate = options.checkInDate
    } else {
      // 默认今天入住
      this.checkInDate = this.formatDateForPicker(new Date())
    }

    if (options.checkOutDate) {
      this.checkOutDate = options.checkOutDate
    } else {
      // 默认明天离店
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      this.checkOutDate = this.formatDateForPicker(tomorrow)
    }

    this.loadHotelDetail()
    this.loadRoomList()
  },

  methods: {
    // 加载酒店详情
    async loadHotelDetail() {
      try {
        uni.showLoading({title: '加载中...'})

        const response = await getHotelDetail(this.hotelId)

        if (response.data.code === 200) {
          const hotelData = response.data.data
          this.hotelInfo = {
            ...hotelData,
            // 处理设施数据，转换为前端需要的格式
            tags: hotelData.facilities || ['免费WiFi', '停车场'],
            facilities: (hotelData.facilities || ['免费WiFi', '停车场', '空调', '电视', '餐厅', '会议室']).map(facility => ({
              name: facility,
              icon: this.getFacilityIcon(facility)
            }))
          }

          // 设置页面标题
          uni.setNavigationBarTitle({
            title: this.hotelInfo.hotelName
          })
        } else {
          uni.showToast({
            title: response.data.message || '获取酒店详情失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取酒店详情失败:', error)
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },

    // 加载房型列表
    async loadRoomList() {
      try {
        const response = await getRoomList({
          hotelId: this.hotelId,
          checkInDate: this.checkInDate,
          checkOutDate: this.checkOutDate
        })

        if (response.data.code === 200) {
          const roomData = response.data.data || []

          // {{ AURA-X: Fix - 保留完整的价格信息，包含priceDetails中的商品ID数据. Approval: 寸止(ID:1753781234). }}
          // 转换数据格式以适配前端显示
          this.roomList = roomData.map((room, index) => ({
            id: room.roomId || (index + 1), // 如果roomId为空，使用索引+1作为id
            name: room.roomName || `房型${index + 1}`,
            size: room.roomArea || 35,
            image: room.roomImages && room.roomImages.length > 0 ? room.roomImages[0] : 'https://images.unsplash.com/photo-1566665797739-1674de7a421a?w=300',
            facilities: room.facilities || ['大床', '海景'],
            price: room.priceInfo ? room.priceInfo.avgPrice : 588,
            priceInfo: room.priceInfo // 保留完整的价格信息，包含priceDetails
          }))

          console.log('🏨 房型列表数据转换完成:', this.roomList)

        } else {
          // 如果接口失败，使用模拟数据
          this.roomList = [
            {
              id: 1,
              name: '精品大床房',
              size: 40,
              image: 'https://images.unsplash.com/photo-1566665797739-1674de7a421a?w=300',
              facilities: ['大床', '海景', '阳台'],
              price: 120
            },
            {
              id: 2,
              name: '豪华大床房',
              size: 35,
              image: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=300',
              facilities: ['双床', '城景', '沙发'],
              price: 100
            },
            {
              id: 3,
              name: '情侣房',
              size: 38,
              image: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=300',
              facilities: ['大床', '电视', 'WiFi'],
              price: 88
            }
          ]

        }
      } catch (error) {
        console.error('获取房型列表失败:', error)
        // 使用模拟数据作为后备
        this.roomList = [
          {
            id: 1,
            name: '精品大床房',
            size: 40,
            image: 'https://images.unsplash.com/photo-1566665797739-1674de7a421a?w=300',
            facilities: ['大床', '海景', '阳台'],
            price: 120
          },
          {
            id: 2,
            name: '豪华大床房',
            size: 35,
            image: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=300',
            facilities: ['双床', '城景', '沙发'],
            price: 100
          },
          {
            id: 3,
            name: '情侣房',
            size: 38,
            image: 'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=300',
            facilities: ['大床', '电视', 'WiFi'],
            price: 88
          }
        ]

      }
    },

    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 分享酒店
    shareHotel() {
      uni.showActionSheet({
        itemList: ['微信好友', '朋友圈', '复制链接'],
        success: (res) => {
          console.log('选择了第' + (res.tapIndex + 1) + '个按钮')
        }
      })
    },

    // 切换介绍展开/收起
    toggleIntro() {
      this.showFullIntro = !this.showFullIntro
    },

    // 选择房型
    selectRoom(room, index) {
      this.selectedRoomId = room.id
      this.selectedRoomName = room.name
      this.selectedRoomIndex = index

      // 给用户反馈
      uni.showToast({
        title: `已选择 ${room.name}`,
        icon: 'success',
        duration: 1500
      })
    },

    // 跳转到日期选择页面
    goToDateSelect() {
      const params = {
        hotelId: this.hotelId,
        checkInDate: this.checkInDate,
        checkOutDate: this.checkOutDate,
        selectedRoomId: this.selectedRoomId || '', // 传递当前选中的房型ID
        selectedRoomName: this.selectedRoomName || '' // 传递房型名称用于显示
      }

      const query = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')

      uni.navigateTo({
        url: `/pages/shopping/hotel/date-select?${query}`
      })
    },

    // 预订房间
    bookRoom(room) {
      // 检查是否有必要的预订信息
      if (!this.checkInDate || !this.checkOutDate) {
        uni.showToast({
          title: '请先选择入住日期',
          icon: 'none'
        })
        return
      }

      if (!room) {
        uni.showToast({
          title: '请选择房型',
          icon: 'none'
        })
        return
      }

      // {{ AURA-X: Fix - 使用API返回的真实价格明细数据，包含商品ID信息. Approval: 寸止(ID:1753781234). }}
      // 构建预订数据对象
      const bookingData = {
        hotelId: this.hotelId,
        hotelName: this.hotelInfo.hotelName,
        roomId: room.id,
        roomName: room.name,
        checkInDate: this.checkInDate,
        checkOutDate: this.checkOutDate,
        nights: this.nightsCount,
        roomPrice: room.price,
        totalPrice: room.priceInfo ? room.priceInfo.totalPrice : (room.price * this.nightsCount),
        priceDetails: room.priceInfo && room.priceInfo.priceDetails ? room.priceInfo.priceDetails : this.generatePriceDetails(room.price)
      }

      console.log('📦 预订数据构建完成:', bookingData)
      console.log('💰 价格明细数据:', bookingData.priceDetails)

      // 将数据编码为URL参数
      const dataParam = encodeURIComponent(JSON.stringify(bookingData))

      uni.navigateTo({
        url: `/pages/shopping/hotel/booking?data=${dataParam}`
      })
    },

    // 快速预订
    quickBook() {
      if (this.roomList.length > 0) {
        this.bookRoom(this.roomList[0])
      } else {
        uni.showToast({
          title: '暂无可预订房型',
          icon: 'none'
        })
      }
    },

    // 生成价格明细
    generatePriceDetails(roomPrice) {
      const details = []
      const checkIn = new Date(this.checkInDate)

      for (let i = 0; i < this.nightsCount; i++) {
        const currentDate = new Date(checkIn)
        currentDate.setDate(currentDate.getDate() + i)

        const dateStr = this.formatDate(this.formatDateForPicker(currentDate))
        const dayOfWeek = currentDate.getDay()

        // 根据星期判断价格类型
        let priceTypeName = '平日价'
        if (dayOfWeek === 0 || dayOfWeek === 6) {
          priceTypeName = '周末价'
        }

        details.push({
          date: dateStr,
          priceTypeName: priceTypeName,
          price: roomPrice
        })
      }

      return details
    },

    // 格式化日期显示
    formatDate(dateStr) {
      if (!dateStr) return '选择日期'
      const date = new Date(dateStr)
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${month}月${day}日`
    },

    // 格式化日期为选择器格式
    formatDateForPicker(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 获取设施图标
    getFacilityIcon(facility) {
      const iconMap = {
        '免费WiFi': '📶',
        'WiFi': '📶',
        '停车场': '🅿️',
        '游泳池': '🏊',
        '健身房': '💪',
        '餐厅': '🍽️',
        '会议室': '👥',
        '空调': '❄️',
        '电视': '📺',
        '独立卫浴': '🚿',
        '吹风机': '💨',
        '拖鞋': '🥿',
        '洗漱用品': '🧴'
      }
      return iconMap[facility] || '🏨'
    }
  }
}
</script>

<style lang="scss" scoped>
.hotel-detail-page {
  background: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 酒店图片轮播 */
.hotel-images {
  position: relative;
  height: 500rpx;

  .swiper {
    height: 100%;
  }

  .hotel-image {
    width: 100%;
    height: 100%;
  }

  .back-btn, .share-btn {
    position: absolute;
    top: 60rpx;
    width: 80rpx;
    height: 80rpx;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }

  .back-btn {
    left: 30rpx;
  }

  .share-btn {
    right: 30rpx;
  }

  .back-icon, .share-icon {
    color: white;
    font-size: 36rpx;
    font-weight: bold;
  }
}

/* 酒店基本信息卡片 */
.hotel-info-card {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

  .hotel-header {
    margin-bottom: 30rpx;

    .hotel-name {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }

    .hotel-rating {
      display: flex;
      align-items: center;
      gap: 20rpx;

      .stars {
        display: flex;
        gap: 4rpx;

        .star {
          font-size: 28rpx;
          color: #ddd;

          &.active {
            color: #FFD700;
          }
        }
      }

      .score {
        font-size: 28rpx;
        color: #666;
        font-weight: 500;
      }
    }
  }

  .hotel-location {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;

    .location-icon {
      font-size: 28rpx;
      margin-right: 16rpx;
    }

    .location-text {
      flex: 1;
      font-size: 28rpx;
      color: #666;
    }

    .distance {
      font-size: 24rpx;
      color: #999;
    }
  }

  .hotel-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;

    .tag {
      background: #f0f8ff;
      color: #1890ff;
      padding: 12rpx 24rpx;
      border-radius: 30rpx;
      font-size: 24rpx;
      border: 1rpx solid #d6e4ff;
    }
  }
}

/* 设施区域 */
.facilities-section {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 40rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }

  .facilities-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30rpx;

    .facility-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      .facility-icon {
        font-size: 48rpx;
        margin-bottom: 16rpx;
      }

      .facility-name {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}

/* 介绍区域 */
.intro-section {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 40rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }

  .intro-content {
    .intro-text {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 20rpx;
    }

    .read-more {
      color: #1890ff;
      font-size: 28rpx;
      cursor: pointer;
    }
  }
}

/* 房型选择区域 */
.rooms-section {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 40rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }

  .date-selector {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f8f8;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 40rpx;

    .date-info {
      text-align: center;

      .date-label {
        display: block;
        font-size: 24rpx;
        color: #999;
        margin-bottom: 8rpx;
      }

      .date-value {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }
    }

    .nights-info {
      text-align: center;

      .nights {
        font-size: 24rpx;
        color: #666;
      }
    }

    .change-date {
      color: #1890ff;
      font-size: 26rpx;
    }
  }

  .room-list {
    .room-item {
      display: flex;
      border: 1rpx solid #f0f0f0;
      border-radius: 16rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      transition: all 0.3s ease;
      cursor: pointer;

      &.selected {
        border-color: #ff6b35;
        background-color: #fff8f5;
        box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.15);
      }

      .room-image {
        width: 160rpx;
        height: 120rpx;
        border-radius: 12rpx;
        overflow: hidden;
        margin-right: 30rpx;

        image {
          width: 100%;
          height: 100%;
        }
      }

      .room-info {
        flex: 1;

        .room-name {
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 12rpx;
        }

        .room-size {
          font-size: 24rpx;
          color: #999;
          margin-bottom: 16rpx;
        }

        .room-facilities {
          display: flex;
          flex-wrap: wrap;
          gap: 12rpx;
          margin-bottom: 20rpx;

          .room-facility {
            background: #f0f8ff;
            color: #1890ff;
            padding: 6rpx 12rpx;
            border-radius: 12rpx;
            font-size: 22rpx;
          }
        }

        .room-price {
          display: flex;
          align-items: baseline;

          .price-symbol {
            font-size: 24rpx;
            color: #ff6b35;
          }

          .price-number {
            font-size: 36rpx;
            font-weight: bold;
            color: #ff6b35;
          }

          .price-unit {
            font-size: 24rpx;
            color: #999;
          }
        }
      }

      .room-action {
        display: flex;
        align-items: center;
        flex-direction: column;
        gap: 16rpx;

        .select-indicator {
          width: 48rpx;
          height: 48rpx;
          background: #ff6b35;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          animation: scaleIn 0.3s ease;
        }

        .book-btn {
          background: #ff6b35;
          color: white;
          border: none;
          border-radius: 30rpx;
          padding: 16rpx 32rpx;
          font-size: 26rpx;
        }
      }
    }
  }
}

/* 动画效果 */
@keyframes scaleIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.booking-hint {
  background-color: #fff;
  padding: 32rpx;
  margin-bottom: 20rpx;

  .hint-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
  }

  .hint-text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
  }
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  padding: 30rpx 40rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;

  .price-info {
    .price-label {
      font-size: 24rpx;
      color: #999;
      display: block;
      margin-bottom: 8rpx;
    }

    .price-value {
      font-size: 32rpx;
      font-weight: bold;
      color: #ff6b35;
    }
  }

  .book-now-btn {
    background: #ff6b35;
    color: white;
    border: none;
    border-radius: 50rpx;
    padding: 24rpx 60rpx;
    font-size: 30rpx;
    font-weight: bold;
  }
}
</style>
